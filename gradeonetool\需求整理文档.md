# 一年级教辅资料归类整理需求文档

## 项目概述

### 目标
将现有的一年级教辅资料按照学科和内容类型进行系统化归类整理，便于后续使用和管理。

### 数据源
- **源文件夹**：
  - `F:\一年级\一年级上册-资料大全` 
  - `F:\一年级\一年级下册-资料大全`
  - `F:\一年级\一年级升二年级-资料大全`
- **目标文件夹**：`F:\一年级\产品分类`
- **文件统计**：总计5775个文件
  - PDF文件：2937个
  - Word文档：2110个（docx+doc）
  - 音频文件：423个（mp3）
  - 视频文件：72个（mp4）
  - PPT文件：150个（ppt+pptx）
  - 其他格式：83个

## 分类体系设计

根据思维导图，在`F:\一年级\产品分类`下建立四个主要文件夹：

### 一、一年级语文上册

#### 1. 幼升小暑假资料
- **幼小衔接暑假练习**
- **电子课本**
- **语文同步练习单**
- **一课一练**
- **语文同步训练**

#### 2. 必背知识点汇总
- **单元核心考点**
- **每课重点总结**
- **各类专项知识必背**

#### 3. 专项训练
- **拼音**
- **生字、字帖**
- **词语、成语**
- **古诗**
- **句子 仿写句子 修改病句**
- **阅读理解**
- **看图写话 每日晨读 作文相关**

#### 4. 试卷
- **单元测试**
- **期中测试**
- **期末测试**
- **各校真题卷**
- **各类模拟卷**

#### 5. 寒假作业巩固
- **下学期预习**
- **假期针对性练习训练**

### 二、一年级语文下册

#### 1. 必背知识点汇总
- **单元核心考点**
- **每课重点总结**
- **各类专项知识必背**

#### 2. 专项训练
- **拼音**
- **生字、字帖**
- **词语、成语**
- **古诗**
- **句子 仿写句子 修改病句**
- **阅读理解**
- **看图写话 每日晨读 作文相关**

#### 3. 试卷
- **单元测试**
- **期中测试**
- **期末测试**
- **各校真题卷**
- **各类模拟卷**

#### 4. 暑假作业巩固
- **下学期预习**
- **假期针对性练习训练**

### 三、一年级数学上册

#### 1. 幼升小暑假资料
- **幼小衔接暑假练习**
- **数字常用字学习**
- **电子课本**
- **课堂笔记**
- **课堂贴**

#### 2. 必背知识点汇总
- **全解公式**
- **单位换算**
- **计算口诀 凑十法 借十法 破十法**
- **单元总结**

#### 3. 专项训练
- **课堂同步训练 一课一练**
- **加减乘除专项训练**
- **口算练习 竖算练习**
- **应用题**
- **思维拓展训练**
- **易错题训练**

#### 4. 试卷
- **单元测试**
- **期中测试**
- **期末测试**
- **各类真题模拟考试卷**

#### 5. 寒假资料
- **寒假作业**
- **上学期知识巩固**
- **下学期预习资料**
- **专项巩固提升训练**

### 四、一年级数学下册

#### 1. 必背知识点汇总
- **全解公式**
- **单位换算**
- **计算口诀 凑十法 借十法 破十法**
- **单元总结**

#### 2. 专项训练
- **课堂同步训练 一课一练**
- **加减乘除专项训练**
- **口算练习 竖算练习**
- **应用题**
- **思维拓展训练**
- **易错题训练**

#### 3. 试卷
- **单元测试**
- **期中测试**
- **期末测试**
- **各类真题模拟考试卷**

#### 4. 暑假资料
- **暑假作业**
- **上学期知识巩固**
- **下学期预习资料**
- **专项巩固提升训练**

## 文件归类规则

### 1. 文件名识别规则
- **学科识别**：通过文件名中的关键词识别学科（数学、语文、英语等）
- **内容类型识别**：通过文件名识别内容类型（练习、试卷、课本、音频等）
- **学期识别**：通过文件名识别学期（上册、下册、暑假、寒假等）

### 2. 文件类型处理
- **PDF文件**：主要为练习册、试卷、课本等
- **Word文档**：主要为练习题、知识点总结等
- **音频文件（MP3）**：主要为课文朗读、听力材料等
- **视频文件（MP4）**：主要为教学视频、讲解视频等
- **PPT文件**：主要为课件、知识点讲解等

### 3. 特殊处理规则
- **重复文件**：检测并处理重复文件
- **文件名规范化**：统一文件命名格式
- **版本管理**：处理同一内容的不同版本

## 目标文件夹结构

### 完整目录结构图

```
F:\一年级\产品分类\
├── 一年级语文上册\
│   ├── 幼升小暑假资料\
│   │   ├── 幼小衔接暑假练习\
│   │   ├── 电子课本\
│   │   ├── 语文同步练习单\
│   │   ├── 一课一练\
│   │   └── 语文同步训练\
│   ├── 必背知识点汇总\
│   │   ├── 单元核心考点\
│   │   ├── 每课重点总结\
│   │   └── 各类专项知识必背\
│   ├── 专项训练\
│   │   ├── 拼音\
│   │   ├── 生字、字帖\
│   │   ├── 词语、成语\
│   │   ├── 古诗\
│   │   ├── 句子 仿写句子 修改病句\
│   │   ├── 阅读理解\
│   │   └── 看图写话 每日晨读 作文相关\
│   ├── 试卷\
│   │   ├── 单元测试\
│   │   ├── 期中测试\
│   │   ├── 期末测试\
│   │   ├── 各校真题卷\
│   │   └── 各类模拟卷\
│   └── 寒假作业巩固\
│       ├── 下学期预习\
│       └── 假期针对性练习训练\
├── 一年级语文下册\
│   ├── 必背知识点汇总\
│   │   ├── 单元核心考点\
│   │   ├── 每课重点总结\
│   │   └── 各类专项知识必背\
│   ├── 专项训练\
│   │   ├── 拼音\
│   │   ├── 生字、字帖\
│   │   ├── 词语、成语\
│   │   ├── 古诗\
│   │   ├── 句子 仿写句子 修改病句\
│   │   ├── 阅读理解\
│   │   └── 看图写话 每日晨读 作文相关\
│   ├── 试卷\
│   │   ├── 单元测试\
│   │   ├── 期中测试\
│   │   ├── 期末测试\
│   │   ├── 各校真题卷\
│   │   └── 各类模拟卷\
│   └── 暑假作业巩固\
│       ├── 下学期预习\
│       └── 假期针对性练习训练\
├── 一年级数学上册\
│   ├── 幼升小暑假资料\
│   │   ├── 幼小衔接暑假练习\
│   │   ├── 数字常用字学习\
│   │   ├── 电子课本\
│   │   ├── 课堂笔记\
│   │   └── 课堂贴\
│   ├── 必背知识点汇总\
│   │   ├── 全解公式\
│   │   ├── 单位换算\
│   │   ├── 计算口诀 凑十法 借十法 破十法\
│   │   └── 单元总结\
│   ├── 专项训练\
│   │   ├── 课堂同步训练 一课一练\
│   │   ├── 加减乘除专项训练\
│   │   ├── 口算练习 竖算练习\
│   │   ├── 应用题\
│   │   ├── 思维拓展训练\
│   │   └── 易错题训练\
│   ├── 试卷\
│   │   ├── 单元测试\
│   │   ├── 期中测试\
│   │   ├── 期末测试\
│   │   └── 各类真题模拟考试卷\
│   └── 寒假资料\
│       ├── 寒假作业\
│       ├── 上学期知识巩固\
│       ├── 下学期预习资料\
│       └── 专项巩固提升训练\
└── 一年级数学下册\
    ├── 必背知识点汇总\
    │   ├── 全解公式\
    │   ├── 单位换算\
    │   ├── 计算口诀 凑十法 借十法 破十法\
    │   └── 单元总结\
    ├── 专项训练\
    │   ├── 课堂同步训练 一课一练\
    │   ├── 加减乘除专项训练\
    │   ├── 口算练习 竖算练习\
    │   ├── 应用题\
    │   ├── 思维拓展训练\
    │   └── 易错题训练\
    ├── 试卷\
    │   ├── 单元测试\
    │   ├── 期中测试\
    │   ├── 期末测试\
    │   └── 各类真题模拟考试卷\
    └── 暑假资料\
        ├── 暑假作业\
        ├── 上学期知识巩固\
        ├── 下学期预习资料\
        └── 专项巩固提升训练\
```

### 简化结构概览

```
产品分类\
├── 一年级语文上册\ (5个主分类)
│   ├── 幼升小暑假资料\ (5个子分类)
│   ├── 必背知识点汇总\ (3个子分类)
│   ├── 专项训练\ (7个子分类)
│   ├── 试卷\ (5个子分类)
│   └── 寒假作业巩固\ (2个子分类)
├── 一年级语文下册\ (4个主分类)
│   ├── 必背知识点汇总\ (3个子分类)
│   ├── 专项训练\ (7个子分类)
│   ├── 试卷\ (5个子分类)
│   └── 暑假作业巩固\ (2个子分类)
├── 一年级数学上册\ (5个主分类)
│   ├── 幼升小暑假资料\ (5个子分类)
│   ├── 必背知识点汇总\ (4个子分类)
│   ├── 专项训练\ (6个子分类)
│   ├── 试卷\ (4个子分类)
│   └── 寒假资料\ (4个子分类)
└── 一年级数学下册\ (4个主分类)
    ├── 必背知识点汇总\ (4个子分类)
    ├── 专项训练\ (6个子分类)
    ├── 试卷\ (4个子分类)
    └── 暑假资料\ (4个子分类)
```

## 实施计划

### 阶段一：数据分析
1. 读取CSV文件，分析现有文件分布
2. 识别文件命名模式和规律
3. 制定详细的分类映射规则

### 阶段二：分类算法开发
1. 开发文件名解析算法
2. 实现自动分类逻辑
3. 处理特殊情况和异常

### 阶段三：文件整理执行
1. 创建目标文件夹结构
2. 执行文件复制/移动操作
3. 生成整理报告

### 阶段四：质量检查
1. 验证分类结果
2. 处理分类错误
3. 完善分类规则

## 技术要求

### 开发工具
- Python 3.13
- 文件操作库（os, shutil, pathlib）
- 数据处理库（pandas, csv）

### 功能要求
1. **批量处理**：能够处理5000+文件
2. **智能识别**：基于文件名自动识别分类
3. **安全操作**：支持复制模式，避免数据丢失
4. **进度跟踪**：显示处理进度和结果统计
5. **错误处理**：妥善处理异常情况
6. **日志记录**：记录所有操作过程

## 预期成果

1. **有序的文件结构**：按学科和内容类型清晰分类
2. **便于查找**：快速定位所需教辅资料
3. **统计报告**：详细的分类统计和处理报告
4. **可维护性**：便于后续添加新资料

---

*文档创建时间：2025-08-22*
*文件总数：5775个*
*预计处理时间：2-3小时*
