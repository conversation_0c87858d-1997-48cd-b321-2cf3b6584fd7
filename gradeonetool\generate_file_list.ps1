# 生成文件清单的PowerShell脚本
# 遍历三个主要文件夹，生成包含文件名和绝对路径的CSV文件

# 设置当前目录
Set-Location "F:\一年级"

# 设置输出CSV文件路径
$outputPath = ".\gradeonetool\file_list.csv"

# 要扫描的文件夹
$folders = @(
    ".\一年级上册-资料大全",
    ".\一年级下册-资料大全",
    ".\一年级升二年级-资料大全"
)

# 创建结果数组
$results = @()

Write-Host "开始扫描文件..."

foreach ($folder in $folders) {
    if (Test-Path $folder) {
        Write-Host "正在扫描: $folder"

        # 递归获取所有文件
        try {
            $files = Get-ChildItem -Path $folder -Recurse -File -ErrorAction Stop

            foreach ($file in $files) {
                $results += [PSCustomObject]@{
                    "原文件名" = $file.Name
                    "绝对路径" = $file.FullName
                    "文件大小KB" = [math]::Round($file.Length / 1KB, 2)
                    "文件扩展名" = $file.Extension
                    "所属文件夹" = Split-Path $file.DirectoryName -Leaf
                    "修改时间" = $file.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
                }
            }
        } catch {
            Write-Warning "扫描文件夹时出错: $folder - $($_.Exception.Message)"
        }
    } else {
        Write-Warning "文件夹不存在: $folder"
    }
}

# 确保输出目录存在
$outputDir = Split-Path $outputPath -Parent
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force
}

# 导出到CSV文件
try {
    $results | Export-Csv -Path $outputPath -Encoding UTF8 -NoTypeInformation
    Write-Host "文件清单已生成: $outputPath"
    Write-Host "总共找到 $($results.Count) 个文件"
} catch {
    Write-Error "导出CSV文件时出错: $($_.Exception.Message)"
}

# 显示统计信息
if ($results.Count -gt 0) {
    Write-Host "`n文件类型统计:"
    $results | Group-Object 文件扩展名 | Sort-Object Count -Descending | ForEach-Object {
        Write-Host "$($_.Name): $($_.Count) 个文件"
    }
}
