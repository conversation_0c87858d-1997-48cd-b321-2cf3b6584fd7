#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成文件清单的Python脚本
遍历三个主要文件夹，生成包含文件名和绝对路径的CSV文件
"""

import os
import csv
import datetime
from pathlib import Path

def get_file_size_kb(file_path):
    """获取文件大小（KB）"""
    try:
        return round(os.path.getsize(file_path) / 1024, 2)
    except:
        return 0

def scan_folder(folder_path):
    """扫描文件夹，返回文件信息列表"""
    files_info = []
    
    if not os.path.exists(folder_path):
        print(f"警告: 文件夹不存在 - {folder_path}")
        return files_info
    
    print(f"正在扫描: {folder_path}")
    
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    stat_info = os.stat(file_path)
                    mod_time = datetime.datetime.fromtimestamp(stat_info.st_mtime)
                    
                    file_info = {
                        '原文件名': file,
                        '绝对路径': file_path,
                        '文件大小KB': get_file_size_kb(file_path),
                        '文件扩展名': os.path.splitext(file)[1],
                        '所属文件夹': os.path.basename(root),
                        '修改时间': mod_time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                    files_info.append(file_info)
                except Exception as e:
                    print(f"处理文件时出错: {file_path} - {e}")
                    
    except Exception as e:
        print(f"扫描文件夹时出错: {folder_path} - {e}")
    
    return files_info

def main():
    """主函数"""
    # 设置基础路径
    base_path = r"F:\一年级"
    
    # 要扫描的文件夹
    folders = [
        os.path.join(base_path, "一年级上册-资料大全"),
        os.path.join(base_path, "一年级下册-资料大全"),
        os.path.join(base_path, "一年级升二年级-资料大全")
    ]
    
    # 输出CSV文件路径
    output_path = os.path.join(base_path, "gradeonetool", "file_list.csv")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    print("开始扫描文件...")
    
    all_files = []
    
    # 扫描所有文件夹
    for folder in folders:
        files_info = scan_folder(folder)
        all_files.extend(files_info)
    
    # 写入CSV文件
    if all_files:
        try:
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['原文件名', '绝对路径', '文件大小KB', '文件扩展名', '所属文件夹', '修改时间']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                writer.writerows(all_files)
            
            print(f"\n文件清单已生成: {output_path}")
            print(f"总共找到 {len(all_files)} 个文件")
            
            # 显示文件类型统计
            ext_count = {}
            for file_info in all_files:
                ext = file_info['文件扩展名']
                if not ext:
                    ext = '(无扩展名)'
                ext_count[ext] = ext_count.get(ext, 0) + 1
            
            print("\n文件类型统计:")
            for ext, count in sorted(ext_count.items(), key=lambda x: x[1], reverse=True):
                print(f"{ext}: {count} 个文件")
                
        except Exception as e:
            print(f"写入CSV文件时出错: {e}")
    else:
        print("没有找到任何文件")

if __name__ == "__main__":
    main()
